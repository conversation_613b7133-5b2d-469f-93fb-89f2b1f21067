<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Japan Itinerary Map</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Leaflet CSS for map rendering -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <!-- Leaflet JavaScript for map functionality -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <style>
        /* Custom styles for the map and itinerary */
        #map { 
            height: 100vh; 
            width: 100%;
        }
        /* Custom transition for smooth scrolling */
        .day-entry {
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        /* Style for the active day in the itinerary */
        .day-entry.active {
            background-color: #eff6ff; /* blue-50 */
            border-left-width: 4px;
            border-color: #3b82f6; /* blue-500 */
        }
        /* Style for the custom marker icons */
        .day-range-marker {
            background-color: #3b82f6; /* blue-500 */
            color: white;
            border-radius: 8px;
            padding: 4px 8px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            border: 2px solid white;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            white-space: nowrap;
        }
        .day-range-marker.active-marker {
             background-color: #ef4444; /* red-500 */
        }
    </style>
</head>
<body class="font-sans antialiased text-gray-800 bg-gray-50">

    <div class="flex flex-col md:flex-row">
        <!-- Itinerary Section -->
        <div id="itinerary" class="w-full md:w-1/3 h-screen overflow-y-auto p-8 bg-white shadow-lg">
            <h1 class="text-3xl font-bold mb-6 text-gray-900">Japan Adventure</h1>
            <div class="space-y-6">
                <!-- Day entries will be dynamically populated here -->
            </div>
        </div>

        <!-- Map Section -->
        <div class="w-full md:w-2/3">
            <div id="map"></div>
        </div>
    </div>

    <script>
        // Itinerary data with locations, dates, and descriptions
        const itineraryData = [
            { day: '3/30', location: 'Tokyo', lat: 35.6895, lon: 139.6917, title: 'Arrival in Tokyo', details: 'Arrive at Narita or Haneda Airport. Transfer to The Blossom Hibiya. Welcome dinner.', accommodation: 'THE BLOSSOM HIBIYA ★★★★★' },
            { day: '3/31', location: 'Tokyo', lat: 35.6895, lon: 139.6917, title: 'Tsukiji & Digital Art', details: 'Tsukiji tour, sushi-making, and teamLab Borderless museum.', accommodation: 'THE BLOSSOM HIBIYA ★★★★★' },
            { day: '4/1', location: 'Tokyo', lat: 35.6895, lon: 139.6917, title: 'Trendy & Traditional Tokyo', details: 'Explore Shibuya, Harajuku, Meiji Shrine, and Asakusa.', accommodation: 'THE BLOSSOM HIBIYA ★★★★★' },
            { day: '4/2', location: 'Ogawa/Chichibu', lat: 36.0525, lon: 139.2942, title: 'Sustainable Farming', details: 'Voluntourism in organic farming in Ogawa, then transfer to Chichibu.', accommodation: 'Wado Onsen - Yuno-Yado Wado ★★★★' },
            { day: '4/3', location: 'Chichibu', lat: 35.9892, lon: 139.0897, title: 'River Boating & Udon', details: 'Nagatoro river boat ride, udon-making, and Mt. Hodo Ropeway.', accommodation: 'Wado Onsen - Yuno-Yado Wado ★★★★' },
            { day: '4/4', location: 'Fujinomiya', lat: 35.2222, lon: 138.6231, title: 'Mt. Fuji Views', details: 'Travel to Fujinomiya for an outdoor BBQ with Mt. Fuji views.', accommodation: 'MT. Fuji Satoyama Vacations ★★★★' },
            { day: '4/5', location: 'Shizuoka', lat: 34.9756, lon: 138.3828, title: 'Waterfalls & E-Biking', details: 'Shiraito Falls walk, e-bike tour, soba lunch, transfer to Shizuoka.', accommodation: 'NIHON IRO ★★★★' },
            { day: '4/6', location: 'Shizuoka', lat: 34.9756, lon: 138.3828, title: 'Crafts & Tea Tasting', details: 'Craft workshop at Takumishuku and a local tea farm visit.', accommodation: 'NIHON IRO ★★★★' },
            { day: '4/7', location: 'Nara', lat: 34.6851, lon: 135.8048, title: 'Ancient Capital Nara', details: 'Shinkansen to Kyoto, then an excursion to Tōdai-ji Temple in Nara.', accommodation: 'Mitsui Garden Hotel Kyoto Sanjo Premier ★★★★★' },
            { day: '4/8', location: 'Kyoto', lat: 35.0116, lon: 135.7681, title: 'Iconic Kyoto & Taiko', details: 'Visit Fushimi Inari, Kinkakuji, and enjoy a Taiko drumming workshop.', accommodation: 'Mitsui Garden Hotel Kyoto Sanjo Premier ★★★★★' },
            { day: '4/9', location: 'Kyoto', lat: 35.0116, lon: 135.7681, title: 'Samurai & Tea Ceremony', details: 'Samurai experience and a traditional tea ceremony. Farewell dinner.', accommodation: 'Mitsui Garden Hotel Kyoto Sanjo Premier ★★★★★' },
            { day: '4/10', location: 'Osaka', lat: 34.6937, lon: 135.5023, title: 'Departure from Osaka', details: 'Transfer to Kansai (KIX) or Itami (ITM) Airport for departure.', accommodation: '' }
        ];

        // Function to group consecutive days by location coordinates
        function groupItineraryByLocation(data) {
            if (!data || data.length === 0) return [];

            const grouped = [];
            let currentGroup = null;

            data.forEach((item, index) => {
                const dayNumber = index + 1;
                const locationKey = `${item.lat},${item.lon}`;

                if (!currentGroup || currentGroup.key !== locationKey) {
                    if (currentGroup) grouped.push(currentGroup);
                    currentGroup = {
                        key: locationKey,
                        lat: item.lat,
                        lon: item.lon,
                        locationName: item.location.split('/')[0], // Use the primary location name
                        startDay: dayNumber,
                        endDay: dayNumber,
                        titles: [item.title],
                    };
                } else {
                    currentGroup.endDay = dayNumber;
                    currentGroup.titles.push(item.title);
                }
                item.groupIdx = grouped.length; // Assign group index to original data
            });
            if (currentGroup) grouped.push(currentGroup);
            return grouped;
        }

        const groupedData = groupItineraryByLocation(itineraryData);
        
        const map = L.map('map').setView([36, 138], 6);

        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            subdomains: 'abcd',
            maxZoom: 20
        }).addTo(map);

        const markers = [];
        const latLngs = groupedData.map(group => [group.lat, group.lon]);

        const itineraryContainer = document.querySelector('#itinerary .space-y-6');

        function updateActiveState(groupIdx) {
            // Update sidebar
            document.querySelectorAll('.day-entry').forEach(el => el.classList.remove('active'));
            document.querySelectorAll(`.day-group-${groupIdx}`).forEach(el => el.classList.add('active'));

            // Update markers
            markers.forEach((marker, index) => {
                const iconElement = marker.getElement()?.querySelector('.day-range-marker');
                if(!iconElement) return;
                if (index === groupIdx) {
                    iconElement.classList.add('active-marker');
                } else {
                    iconElement.classList.remove('active-marker');
                }
            });
        }
        
        // Create markers from grouped data
        groupedData.forEach((group, index) => {
            const dayLabel = group.startDay === group.endDay ? group.startDay : `${group.startDay}-${group.endDay}`;
            const icon = L.divIcon({
                html: `<div class="day-range-marker">${dayLabel}</div>`,
                className: 'custom-div-icon',
                iconSize: L.point(dayLabel.length * 10 + 20, 30),
                iconAnchor: [ (dayLabel.length * 10 + 20) / 2, 30]
            });

            const marker = L.marker([group.lat, group.lon], { icon: icon }).addTo(map)
                .bindPopup(`<b>${group.locationName}</b><br>Days ${dayLabel}`);
            markers.push(marker);

            marker.on('click', () => {
                 updateActiveState(index);
                 const firstDayOfGroup = document.getElementById(`day-${group.startDay - 1}`);
                 if(firstDayOfGroup) {
                     firstDayOfGroup.scrollIntoView({ behavior: 'smooth', block: 'center' });
                 }
            });
        });

        // Create individual day entries in the sidebar
        itineraryData.forEach((item, index) => {
            const dayElement = document.createElement('div');
            dayElement.className = `day-entry p-4 rounded-lg cursor-pointer hover:bg-blue-50 day-group-${item.groupIdx}`;
            dayElement.id = `day-${index}`;
            dayElement.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h2 class="text-xl font-bold text-gray-800">${item.location}</h2>
                    <span class="text-sm font-medium text-gray-500">Day ${index + 1} | ${item.day}</span>
                </div>
                <h3 class="text-lg font-semibold text-blue-600 mb-1">${item.title}</h3>
                <p class="text-gray-600 text-sm mb-2">${item.details}</p>
                ${item.accommodation ? `<p class="text-xs text-gray-500 font-medium"><b>Stay:</b> ${item.accommodation}</p>` : ''}
            `;

            dayElement.addEventListener('click', () => {
                const group = groupedData[item.groupIdx];
                map.flyTo([group.lat, group.lon], 11);
                markers[item.groupIdx].openPopup();
                updateActiveState(item.groupIdx);
                dayElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            });
            itineraryContainer.appendChild(dayElement);
        });
        
        const polyline = L.polyline(latLngs, { color: '#3b82f6', weight: 3, opacity: 0.8 }).addTo(map);
        map.fitBounds(polyline.getBounds().pad(0.2));

        // Highlight the first group by default
        updateActiveState(0);

    </script>
</body>
</html>
