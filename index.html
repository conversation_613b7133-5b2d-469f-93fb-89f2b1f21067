<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Japan: Beyond the Torii Gates - Xploraway Itinerary</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Noto Sans JP', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
      background-color: var(--color-bg-light);
      color: var(--color-primary-text);
      scroll-behavior: smooth;
    }

    /* Custom Colors - Japanese Inspired Theme */
    :root {
      --color-primary-text: #2F3A4C; /* Deep Indigo/Dark Slate Blue */
      --color-heading-accent: #BC4749; /* Traditional Sienna Red */
      --color-secondary-accent: #6A994E; /* Forest/Bamboo Green */
      --color-border: #DCD5C9; /* Softer, darker linen for borders */
      --color-bg-light: #FAF3E0; /* Warm Linen/Off-white */
      --color-bg-card: #FFFFFF; 
      --color-bg-header-footer: #F5F0E6; /* Very light linen variant */
      --color-bg-accordion-header: #EDE7D9; /* Slightly darker linen */
      --color-icon-default: var(--color-primary-text);
      --color-icon-accent1: var(--color-heading-accent);
      --color-icon-accent2: var(--color-secondary-accent);
      --color-icon-neutral: #757575; /* Medium Grey for neutral icons */
      --color-hero-text: #FFFFFF;
      --color-hero-subtitle-accent: #FFFFFF; 
      --color-black: #000000;
      --color-gold: #C0A062; /* Sophisticated Muted Gold */
      --color-bright-gold: #FACC15; /* Bright Yellow-Gold */
    }

    .text-primary-text { color: var(--color-primary-text); }
    .text-heading-accent { color: var(--color-heading-accent); }
    .hover\:text-heading-accent-dark:hover { color: #A03C3E; } /* Darker sienna */
    .text-secondary-accent { color: var(--color-secondary-accent); }
    .text-gold { color: var(--color-gold); }
    .border-custom { border-color: var(--color-border); }
    .border-gold { border-color: var(--color-gold); }
    .bg-custom-light { background-color: var(--color-bg-light); }
    .bg-custom-card { background-color: var(--color-bg-card); }
    .bg-custom-header-footer { background-color: var(--color-bg-header-footer); }
    .bg-custom-accordion-header { background-color: var(--color-bg-accordion-header); }

    /* Hero Background */
    #hero {
      background-image: url('https://www.masterpiece-of-japanese-culture.com/wp-content/uploads/2018/03/800px-Fushimi_Inari1.jpg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    /* Itinerary Accordion */
    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out, padding 0.3s ease-out;
      padding-top: 0;
      padding-bottom: 0;
    }
    .accordion-content.open {
      max-height: 10000px; /* Increased max-height for images */
      padding-top: 1.5rem; /* p-6 */
      padding-bottom: 1.5rem; /* p-6 */
    }

    /* Icon styles to match theme */
    .icon {
        display: inline-block;
        width: 1.125em; 
        height: 1.125em;
        margin-right: 0.375em; /* mr-1.5 */
        flex-shrink: 0;
        vertical-align: middle; 
    }
   
    /* Link styling */
    a {
      color: var(--color-secondary-accent);
      transition: color 0.2s ease-in-out;
    }
    a:hover {
      color: #53783C; /* Darker shade of secondary accent */
    }

    h1, h2, h3, h4 {
        color: var(--color-heading-accent);
        font-weight: 700; 
    }
    h1.hero-title { 
      color: var(--color-hero-text); 
      text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.6); /* Drop shadow for hero title */
    } 
    h1.hero-title .hero-title-main { color: var(--color-heading-accent); } /* "Japan" in sienna red */
    h1.hero-title .hero-title-tagline { color: var(--color-bright-gold); } /* "Beyond the Torii Gates" in bright gold */


    h2 { /* Section Titles */
        font-size: 2.25rem; /* text-4xl */
        line-height: 2.5rem;
        margin-bottom: 1.75rem; /* mb-7 */
    }
    h3 { /* Major Sub-headings (if used, currently highlights title is h2) */
        font-size: 1.875rem; /* text-3xl */
        line-height: 2.25rem;
        margin-bottom: 1.25rem; /* mb-5 */
    }
    h4 { /* Activity titles, Accommodation Titles */
        font-size: 1.5rem; /* text-2xl */
        line-height: 2rem;
        margin-bottom: 0.75rem; /* mb-3 */
    }
    .content-section {
      padding-top: 3.5rem; 
      padding-bottom: 3.5rem; 
    }
    .content-section.first-section {
      padding-top: 0;
    }

    .card {
        background-color: var(--color-bg-card);
        border: 1px solid var(--color-border);
        border-radius: 0.5rem; /* rounded-lg */
        box-shadow: 0 4px 6px -1px rgba(47, 58, 76, 0.07), 0 2px 4px -1px rgba(47, 58, 76, 0.04);
    }
    .card-xl { /* For pricing section */
        background-color: var(--color-bg-card);
        border: 1px solid var(--color-border);
        border-radius: 0.75rem; /* rounded-xl */
        box-shadow: 0 10px 15px -3px rgba(47, 58, 76, 0.08), 0 4px 6px -2px rgba(47, 58, 76, 0.05); /* Enhanced shadow */
    }


    .btn-primary {
        background-color: var(--color-heading-accent);
        color: white;
        padding: 0.625rem 1.25rem; 
        border-radius: 0.375rem; /* rounded-md */
        transition: background-color 0.2s ease;
        font-size: 1rem; /* text-base */
        text-decoration: none; /* Ensure buttons that are links don't have underlines */
    }
    .btn-primary:hover {
        background-color: #A03C3E; /* Darker shade */
    }
    .btn-secondary { /* For Open/Close All button */
        background-color: var(--color-secondary-accent);
        color: white;
        padding: 0.625rem 1.25rem; 
        border-radius: 0.375rem; /* rounded-md */
        transition: background-color 0.2s ease;
        font-size: 1rem; /* text-base */
        text-decoration: none;
    }
    .btn-secondary:hover {
        background-color: #53783C; /* Darker shade */
    }
    
    .logo-image {
      max-height: 48px; 
      width: auto;
    }

    .prose-lg { 
      color: var(--color-primary-text);
      font-size: 1.125rem; /* text-lg */
      line-height: 1.7777778; 
    }
    .prose-lg p {
      margin-bottom: 1.25em;
    }
    .prose-lg ul {
      margin-bottom: 1.25em;
    }
    .prose-lg li {
      margin-bottom: 0.5em;
    }
    .text-lg { 
      font-size: 1.125rem;
      line-height: 1.75rem; 
    }

    .itinerary-image-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Responsive grid */
        gap: 1.5rem; /* Increased gap for better spacing */
        margin-top: 1.5rem; /* mt-6 */
        margin-bottom: 1.5rem; /* mb-6 */
        max-width: 1200px; /* Maximum width for the container */
        margin-left: auto;
        margin-right: auto;
    }
    .itinerary-image-item {
        position: relative;
        width: 100%;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        overflow: hidden;
        border-radius: 0.5rem; /* rounded-lg */
        border: 1px solid var(--color-border);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        background-color: var(--color-bg-light);
        max-width: 500px; /* Maximum width for each image container */
        margin: 0 auto; /* Center the container */
    }
    .itinerary-image-item img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain; /* Changed from cover to contain */
        padding: 0.75rem; /* Increased padding around images */
        background-color: var(--color-bg-light);
        max-width: 100%;
        max-height: 100%;
    }

    /* Special handling for single images */
    .itinerary-image-container:has(.itinerary-image-item:only-child) {
        max-width: 800px;
    }
    .itinerary-image-container:has(.itinerary-image-item:only-child) .itinerary-image-item {
        max-width: 600px;
        padding-bottom: 45%; /* Shorter aspect ratio for single images */
    }

    /* Media queries for responsive image sizing */
    @media (max-width: 768px) {
        .itinerary-image-container {
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1rem;
        }
        .itinerary-image-item {
            padding-bottom: 66.67%; /* 3:2 aspect ratio for mobile */
            max-width: 100%;
        }
        .itinerary-image-container:has(.itinerary-image-item:only-child) .itinerary-image-item {
            padding-bottom: 56.25%; /* 16:9 aspect ratio for single images on mobile */
        }
    }

    @media (min-width: 769px) and (max-width: 1024px) {
        .itinerary-image-container {
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        }
    }

    #scrollToTopBtn {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: var(--color-heading-accent);
      color: white;
      border: none;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      font-size: 24px;
      line-height: 50px; 
      text-align: center;
      cursor: pointer;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 1000;
      transition: opacity 0.3s, visibility 0.3s, background-color 0.2s, bottom 0.3s ease-in-out, right 0.3s ease-in-out, width 0.3s ease-in-out, height 0.3s ease-in-out;
    }
    #scrollToTopBtn:hover {
      background-color: #A03C3E; 
    }

    /* Desktop Day Navigation */
    #day-navigation {
      position: fixed;
      top: 50%;
      left: 1rem; /* left-4 */
      transform: translateY(-50%);
      z-index: 45;
      transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }
    #day-navigation ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 0.375rem; /* Adjusted gap */
    }
    #day-navigation a {
      display: block;
      padding: 0.5rem 0.75rem; /* py-2 px-3 - Increased padding */
      font-size: 0.875rem; /* text-sm - Increased font size */
      font-weight: 500; /* font-medium */
      color: var(--color-primary-text);
      background-color: rgba(255, 255, 255, 0.85); /* bg-white/85 - Slightly more opaque */
      border: 1px solid var(--color-border);
      border-radius: 0.375rem; /* rounded-md */
      text-decoration: none;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.07), 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* Slightly stronger shadow */
      transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out, border-color 0.15s ease-in-out;
      backdrop-filter: blur(3px); /* Slightly stronger blur */
    }
    #day-navigation a:hover {
      background-color: var(--color-heading-accent);
      color: white;
      border-color: var(--color-heading-accent);
    }
    #day-navigation.hidden {
        opacity: 0;
        visibility: hidden;
    }

    /* Mobile specific styles */
    @media (max-width: 768px) {
      main {
        padding-bottom: 3.5rem; /* Approx nav height + buffer */
      }

      #day-navigation {
        top: auto; 
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        transform: none; 
        background-color: var(--color-bg-header-footer);
        padding: 0.5rem 0; 
        box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.08); 
        border-top: 1px solid var(--color-border);
        backdrop-filter: blur(5px); 
      }

      #day-navigation ul {
        flex-direction: row; 
        overflow-x: auto;   
        overflow-y: hidden;
        white-space: nowrap;  
        justify-content: flex-start; 
        align-items: center;
        padding: 0 0.75rem;  
        gap: 0.5rem;       
        -webkit-overflow-scrolling: touch; 
      }

      #day-navigation ul::-webkit-scrollbar {
        display: none;
      }
      #day-navigation ul {
        -ms-overflow-style: none;  
        scrollbar-width: none;  
      }

      #day-navigation a {
        padding: 0.4rem 0.8rem; 
        font-size: 0.8rem;    
        flex-shrink: 0;       
      }

      #scrollToTopBtn {
        bottom: 75px; 
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 20px;
        line-height: 45px;
      }
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
</head>
<body class="bg-custom-light text-primary-text">

  <header id="main-header" class="bg-custom-header-footer shadow-md sticky top-0 z-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
      <a href="https://stage.xploraway.com/" target="_blank" rel="noopener noreferrer" class="flex items-center space-x-2" aria-label="Xploraway Home">
        <img src="https://stage.xploraway.com/wp-content/uploads/2025/05/Xploraway-logo.png" alt="Xploraway Logo" class="logo-image" onerror="this.style.display='none'">
      </a>
      <div class="text-right text-lg text-primary-text">
        <p class="font-semibold">Japan: Beyond the Torii Gates</p>
        <p>Travel Dates: March 30 - April 10, 2026</p>
      </div>
    </div>
  </header>

  <nav id="day-navigation" class="hidden" aria-label="Daily Itinerary Navigation">
    <ul role="menu">
      <!-- Links will be populated by JavaScript -->
    </ul>
  </nav>

  <main>
    <section id="hero" class="relative text-white h-[60vh] min-h-[450px] md:h-[70vh] flex items-center justify-center overflow-hidden content-section first-section">
      <div class="relative z-10 text-center p-6 sm:p-8 bg-black/40 rounded-lg backdrop-blur-sm max-w-4xl">
        <h1 class="hero-title text-5xl sm:text-6xl md:text-7xl font-bold leading-tight">
          <span class="hero-title-main">Japan</span>: <span class="hero-title-tagline">Beyond the Torii Gates</span>
        </h1>
        <p class="mt-4 text-2xl sm:text-3xl md:text-4xl font-light text-hero-subtitle-accent">
          Discover the Beating Heart of Japan.
        </p>
        <p class="mt-6 text-xl sm:text-2xl text-gray-200">12 Days | March 30 - April 10, 2026</p>
      </div>
    </section>

    <section id="disclaimer-banner" class="py-3 bg-custom-light text-center">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <p class="text-sm italic text-primary-text opacity-80">
          Please note: This itinerary is a work in progress and subject to change.
        </p>
      </div>
    </section>

    <section id="overview" class="content-section bg-custom-card">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-5 gap-8 lg:gap-12">
          <div class="md:col-span-3">
            <h2 class="text-heading-accent">Overview</h2>
            <div class="space-y-4 text-primary-text prose-lg max-w-none">
              <p>This 12-day, all-inclusive journey offers the perfect blend of Japan's iconic highlights and immersive, hands-on experiences for curious, active travelers. While covering must-see destinations like Tokyo and Kyoto, the itinerary also explores lesser-known areas—hidden gems that remain peaceful even during the busy cherry blossom season.</p>
              <p>Rather than simply visiting famous sites, you'll engage deeply with Japanese culture: make sushi and udon with local masters, experience a traditional tea ceremony, feel the rhythm of Taiko drums, craft your own chopsticks, and explore the countryside by E-bike or riverboat—all while discovering Japan's rich food culture, arts, and natural beauty.</p>
              <p>A unique highlight of the trip is a visit to Japan's leading organic farming town, where you'll take part in meaningful voluntourism activities such as organic farming and forest conservation. These experiences offer a chance to give back, learn about sustainability, and connect with local communities.</p>
              <p>Designed to minimize transit time and maximize cultural immersion, the itinerary combines comfort, adventure, and authenticity. Accommodations range from glamping tents and traditional ryokans to high-end hotels. Activities are suitable for both adults and teens, making this a rewarding and memorable experience for families.</p>
            </div>
          </div>
          <div class="md:col-span-2">
            <h2 class="text-heading-accent">Highlights</h2>
            <ul class="space-y-3 text-lg text-primary-text leading-relaxed">
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Cultural Immersion: Sushi & udon workshops, chopstick crafting, tea ceremony, Taiko drum experience</span>
              </li>
               <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Experience the Way of the Samurai and delve into Bushido philosophy.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Sustainable Voluntourism: Organic farming and forest conservation projects.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Discover off-the-beaten-path locations like Ogawamachi and Chichibu for authentic local encounters.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Hands-On Nature Activities: E-bike tours, river boating.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Family-Friendly Design: Engaging for both adults and teens.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Adventure with Comfort: Mix of glamping, ryokans, and high-end hotels.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">Seasonal Beauty: Timed for cherry blossoms and spring harvests.</span>
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon mt-1" style="color: var(--color-secondary-accent);"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                <span class="ml-1">All-Inclusive Simplicity: Includes most meals, transport, guides, fees, and activities.</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="content-section bg-custom-light"> 
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-heading-accent text-center">Trip Pricing</h2>
        <div class="mt-8 max-w-3xl mx-auto text-lg text-primary-text space-y-6"> 
          <div class="card-xl p-6 md:p-8 bg-custom-card shadow-xl rounded-xl"> 
              <h3 class="text-3xl font-semibold text-heading-accent mb-8 text-center">Your Journey's Investment</h3>
              <div class="grid md:grid-cols-2 gap-6 lg:gap-8 mb-8">
                  <div class="p-6 bg-custom-light rounded-lg border-l-4 border-gold shadow-lg hover:shadow-xl transition-shadow duration-300">
                      <p class="text-base font-medium text-primary-text opacity-90 mb-2">Adult (12+ years)</p>
                      <p class="text-4xl font-bold text-secondary-accent mb-1">$9,999 <span class="text-xl font-normal text-primary-text">USD</span></p>
                      <p class="text-sm text-primary-text opacity-75">per person</p>
                  </div>
                  <div class="p-6 bg-custom-light rounded-lg border-l-4 border-gold shadow-lg hover:shadow-xl transition-shadow duration-300">
                      <p class="text-base font-medium text-primary-text opacity-90 mb-2">Child (under 12)</p>
                      <p class="text-4xl font-bold text-secondary-accent mb-1">$9,850 <span class="text-xl font-normal text-primary-text">USD</span></p>
                      <p class="text-sm text-primary-text opacity-75">per person</p>
                  </div>
              </div>
              <div class="text-base text-primary-text opacity-95 space-y-4 border-t border-custom pt-6">
                <p class="italic flex items-start"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon w-5 h-5 mr-2 text-gold flex-shrink-0"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" /></svg><span>Prices are based on double occupancy. Single supplement may be available upon request. Please inquire for details.</span></p>
                <p class="italic flex items-start"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon w-5 h-5 mr-2 text-gold flex-shrink-0"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" /></svg><span>This is an all-inclusive journey. Includes accommodations, most meals, ground transportation, guides, entrance fees, and all listed activities as per the itinerary.</span></p>
                <div class="bg-custom-light p-4 rounded-lg border border-gold">
                  <h4 class="text-xl font-semibold text-heading-accent mb-3">Peak Season Value</h4>
                  <p class="mb-3">This 12-day luxury journey is timed for Japan's most beautiful season—cherry blossom time—when demand is extremely high. While other companies offer 10-day tours at $14,000+ for similar dates, our comprehensive 12-day itinerary provides:</p>
                  <ul class="list-disc pl-6 space-y-2">
                    <li>Two extra days of immersive experiences</li>
                    <li>Luxury accommodations throughout</li>
                    <li>More hands-on cultural activities</li>
                    <li>Smaller group size for personalized attention</li>
                    <li>Exclusive access to hidden gems</li>
                  </ul>
                </div>
                <div class="bg-custom-light p-4 rounded-lg border border-gold">
                  <h4 class="text-xl font-semibold text-heading-accent mb-3">Activity Levels & Flexibility</h4>
                  <p class="mb-3">This journey is designed for active travelers who enjoy a mix of cultural immersion and physical activities. Most activities are moderate in intensity, with options to adjust based on your comfort level. Key activities include:</p>
                  <ul class="list-disc pl-6 space-y-2">
                    <li>Light hiking at Fushimi Inari and mountain trails</li>
                    <li>E-bike tours (suitable for all fitness levels)</li>
                    <li>Hands-on workshops (standing for 1-2 hours)</li>
                    <li>Walking tours (2-3 hours with breaks)</li>
                  </ul>
                </div>
                <p class="italic flex items-start"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="icon w-5 h-5 mr-2 text-gold flex-shrink-0"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" /></svg><span>Pre and post-trip stays can be arranged upon request. Please inquire about additional nights in Tokyo or other destinations.</span></p>
              </div>
          </div>
          <div class="text-center mt-10">
            <a href="mailto:<EMAIL>?subject=Inquiry%20about%20Japan:%20Beyond%20the%20Torii%20Gates%20Trip" class="btn-primary inline-block text-lg py-3 px-8">
              Inquire Now / Book Your Adventure
            </a>
          </div>
        </div>
      </div>
    </section>
    <!-- End Pricing Section -->

    <section id="itinerary" class="content-section bg-custom-light">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6 md:mb-8">
          <h2 class="text-heading-accent text-center md:text-left flex-grow">Daily Itinerary</h2>
          <button id="toggle-all-accordions" class="btn-secondary ml-4">Open All Days</button>
        </div>
        <div id="itinerary-accordion-container" class="space-y-4 md:space-y-6">
          <!-- Itinerary will be populated by JavaScript -->
        </div>
      </div>
    </section>
  </main>

  <footer class="bg-custom-header-footer text-primary-text py-8 mt-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="flex justify-center items-center space-x-2 mb-4">
        <a href="https://stage.xploraway.com/" target="_blank" rel="noopener noreferrer" class="hover:opacity-80 transition-opacity">
          <img src="https://stage.xploraway.com/wp-content/uploads/2025/05/Xploraway-logo.png" alt="Xploraway Logo" class="logo-image" onerror="this.style.display='none'">
        </a>
      </div>
      <p class="text-base mt-1"> 
        &copy; <span id="current-year"></span> Xploraway. All rights reserved.
      </p>
    </div>
  </footer>

  <button id="scrollToTopBtn" title="Go to top" class="hidden">↑</button>

  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();

    const itineraryData = {
      dailyPlans: [
        {
          day: 1,
          date: "3/30",
          location: "Tokyo",
          mainActivityTitle: "Arrive in Tokyo & Welcome Dinner",
          narrative: [
            "Arrive at Narita or Haneda Airport in Tokyo, where your driver will greet you in the arrivals area with a name placard. You'll be transferred to your hotel by private vehicle.",
            "Check into your hotel. The Blossom Hibiya is a great place to stay if you want to be in the heart of Tokyo. It's right near Shimbashi Station, so it's easy to get around and explore spots like Ginza, Hibiya Park, and the Imperial Palace. All the rooms are up high—19th floor or above—so the views are amazing. The hotel has everything you need: comfy rooms, fast Wi-Fi, a gym, and a nice lounge. The style is a mix of modern and Japanese touches. Breakfast is amazing and there are tons of places to eat nearby. Super convenient and relaxed, with a nice touch of style."
          ],
          activities: [
            {
              title: "Welcome Dinner",
              description: ["Enjoy a specially arranged welcome dinner at a local restaurant. This casual gathering is an opportunity to meet fellow travelers, review the trip itinerary, and set the tone for the journey ahead."],
            }
          ],
          images: [
            { src: "blossom hibiya flowers.png", alt: "Cherry blossoms in The Blossom Hibiya hotel lobby" },
            { src: "blossom hibiya.png", alt: "Guest room at The Blossom Hibiya hotel with city view" }
          ],
          mealsIncluded: "Dinner",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "THE BLOSSOM HIBIYA",
            meals: "D Included (Welcome Dinner this night, B next day)",
            link: "https://www.jrk-hotels.co.jp/Hibiya/",
            stars: "★★★★★"
          }
        },
        {
          day: 2,
          date: "3/31",
          location: "Tokyo",
          mainActivityTitle: "Tokyo Sushi-Making & Digital Art Experience",
          narrative: ["Breakfast at Hotel."],
          activities: [
            {
              title: "Tsukiji & Sushi Making Tour",
              description: ["Your day begins with a 2-hour tour of the Tsukiji area with a local guide, including tasting street food and shopping for essential sushi ingredients. Afterwards, enjoy an exclusive hands-on sushi-making experience with guidance from a local expert as you craft your own sushi. Along the way, you'll also learn about Japan's seasonal food culture and deep culinary traditions."],
            },
            {
              title: "teamLab Immersive Digital Art Museum",
              description: ["In the afternoon, shift into a different kind of sensory journey with a visit to teamLab Borderless—a world-famous immersive digital art museum where boundaries between viewer and artwork disappear. This vibrant, interactive space offers a perfect fusion of art and technology that fascinates both adults and children alike."],
            },
            {
              title: "Dinner at a Local Restaurant",
              description: ["Enjoy dinner together at a restaurant recommended by your guide, who knows the area well. It's a great chance to relax and experience local flavors with the group."]
            }
          ],
          images: [
            { src: "sushi making.png", alt: "Hands-on sushi making experience" },
            { src: "teamlab.png", alt: "Immersive digital art at teamLab Borderless" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "THE BLOSSOM HIBIYA",
            meals: "B Included",
            link: "https://www.jrk-hotels.co.jp/Hibiya/",
            stars: "★★★★★"
          }
        },
        {
          day: 3,
          date: "4/1",
          location: "Tokyo",
          mainActivityTitle: "Full Guided Tour of Modern Trends and Old-Town Charm in Tokyo",
          narrative: ["Breakfast at Hotel."],
          activities: [
            {
              title: "Explore Trendy Tokyo",
              description: ["Spend the day exploring Tokyo with your local guide. Start by exploring the vibrant vibes of Shibuya, the trendy charm of Cat Street, the quirky energy of Harajuku, and the serene beauty of Meiji Shrine. When lunchtime rolls around, trust your guide to lead you to a well-loved local restaurant for a flavorful lunch."],
            },
            {
              title: "Explore the Old-Town Charm of Tokyo",
              description: ["In the afternoon, head over to Ueno, Asakusa, or other parts of Tokyo's traditional east side. Your guide will have plenty of tips and suggestions based on your interests and energy level."],
            },
            {
              title: "Free Time for Evening",
              description: ["Expect to return to your hotel by around 5pm. This evening is yours to enjoy—choose whatever you like for dinner and explore at your own pace. There are plenty of great restaurants nearby, all within walking distance or a short taxi ride. Of course, your local guide will be happy to offer recommendations."]
            }
          ],
          images: [
            { src: "tokyo city.png", alt: "Modern Tokyo cityscape" },
            { src: "toykyo temple.png", alt: "Traditional temple in Tokyo" }
          ],
          mealsIncluded: "Breakfast, Lunch",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "THE BLOSSOM HIBIYA",
            meals: "B Included",
            link: "https://www.jrk-hotels.co.jp/Hibiya/",
            stars: "★★★★★"
          }
        },
        {
          day: 4,
          date: "4/2",
          location: "Ogawa/Chichibu",
          mainActivityTitle: "Voluntourism: Farming and Forest Conservation",
          narrative: ["Breakfast at Hotel."],
          activities: [
            {
              title: "Voluntourism for Sustainable Farm Work",
              description: ["Travel to Ogawa Town, a satoyama area nestled in the northern suburbs of Tokyo, known today as one of Japan's leading organic farming communities. This transformation began with one passionate farmer who pioneered organic practices there, mentoring many trainees who would go on to spread and deepen the movement through the town. Experience voluntourism by taking part in sustainable farm work and learning firsthand about environmentally conscious agricultural practices, while supporting a community rooted in care for the land and future generations."],
            },
            {
              title: "Voluntourism for Sustainable Forest Work",
              description: ["After enjoying a seasonal lunch made with fresh organic vegetables, continue your journey with a meaningful forest restoration activity. This hands-on project supports the sustainable management of local forests before transferring to Chichibu. Across Japan, many traditional satoyama forests and mountains are no longer actively used. In some areas, large-scale deforestation is taking place to make way for mega-solar developments, raising serious environmental concerns. Join a sustainability-driven project that protects these valuable ecosystems, and learn about the challenges Japan's forests face today—and the grassroots efforts aiming to overcome them."],
            },
            {
              title: "Dinner at the Ryokan",
              description: ["Enjoy a traditional Kaiseki dinner at your Ryokan. *A Ryokan is a traditional Japanese-style inn, often featuring tatami floors, futon bedding, and elaborate multi-course meals."]
            }
          ],
          images: [
            { src: "farming voluntourism.png", alt: "Group participating in farming voluntourism" },
            { src: "forest voluntourism.png", alt: "Group photo during forest conservation voluntourism" },
            { src: "onsen Ryokan.jpg", alt: "Traditional Japanese Ryokan with onsen" },
            { src: "Ogawa Town.jpg", alt: "Scenic view of Ogawa Town" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "Wado Onsen - Yuno-Yado Wado",
            meals: "B & D Included",
            link: "https://www.wadoh.co.jp/en/",
            stars: "★★★★"
          }
        },
        {
          day: 5,
          date: "4/3",
          location: "Chichibu",
          mainActivityTitle: "Traditional River Boating and Udon Making",
          narrative: ["Breakfast at Ryokan."],
          activities: [
            {
              title: "Traditional River Boating in Nagatoro",
              description: ["Enjoy a refreshing and exciting river boat ride in Nagatoro, as you gently float down the scenic Arakawa River. Take in the stunning natural landscapes and the famous layered rock formations known as Iwadatami. It's a fun and safe experience that can be enjoyed by both children and adults alike."],
            },
            {
              title: "Udon Noodle-Making Workshop",
              description: ["Take part in a hands-on udon noodle-making session. In this region, udon has long been a local favorite, with many families traditionally making their own noodles at home. Learn tips and techniques from a professional udon maker, who will share his expert knowledge on what makes great udon. After the workshop, enjoy tasting the freshly made noodles with some Tempura—especially satisfying when you've made them yourself!"],
            },
            {
              title: "Shrine Visit & Mt. Hodo Ropeway",
              description: ["Visit the historic Hodosan Shrine, surrounded by nature and seasonal beauty. Afterwards, take the ropeway up Mt. Hodo and enjoy stunning views of the surrounding mountains, along with seasonal flowers in bloom. You'll see just how mountainous Japan is—even right next to Tokyo!"]
            },
            {
              title: "Dinner at the Ryokan",
              description: ["Savor another delightful dinner at your traditional Japanese inn."]
            }
          ],
          images: [
            { src: "boating.png", alt: "Scenic river boating experience" },
            { src: "Mt. Hodo Ropeway.jpg", alt: "Scenic view from Mt. Hodo Ropeway" },
            { src: "udon making.webp", alt: "Hands-on udon noodle making workshop" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "Wado Onsen - Yuno-Yado Wado",
            meals: "B & D Included",
            link: "https://www.wadoh.co.jp/en/",
            stars: "★★★★"
          }
        },
        {
          day: 6,
          date: "4/4",
          location: "Higashi Chichibu/Fujinomiya",
          mainActivityTitle: "Cherry Blossoms at a Hidden Gem & Mt. Fuji View BBQ",
          narrative: [
            "Breakfast at Ryokan.",
            "Travel to Fujinomiya. Weather permitting, make a stop at the hidden gem of Higashi Chichibu Village, where you can enjoy panoramic cherry blossom views across the mountain slopes. Unlike the famous spots in Tokyo that are often extremely crowded, this peaceful local site is less touristy and has been lovingly maintained by the local community for generations. A small conservation fee will be offered on behalf of the group to support their preservation efforts.",
            "Enjoy a lunch stop at a local restaurant en route."
          ],
          activities: [
            {
              title: "Outdoor BBQ Dinner with Mt. Fuji Views",
              description: ["Upon arriving at your Mt. Fuji glamping site, take in the majestic views of Mt. Fuji stretching out before you with no crowds to block the scenery, just open skies and peaceful surroundings. After a bit of rest, enjoy an outdoor BBQ dinner featuring local ingredients, including fresh vegetables and the region's renowned Asagiri meat."]
            }
          ],
          images: [
            { src: "bbq meat fuji.png", alt: "BBQ setup with Mt. Fuji in the background" },
            { src: "glamping night.jpg", alt: "Night view of Mt. Fuji glamping site" },
            { src: "gamping inside.webp", alt: "Interior of Mt. Fuji glamping accommodation" },
            { src: "cherry-blossoms-on-mt-yoshino-308731.jpg", alt: "Cherry blossoms on mountain slopes at Higashi Chichibu Village" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Private chartered mid-size bus",
          notes: "Sakura timing may vary—alternative scenic stops available.",
          accommodation: {
            name: "MT. Fuji Satoyama Vacations",
            meals: "B & D Included",
            link: "https://www.mtfujiecotours.com/stay/",
            stars: "★★★★"
          }
        },
        {
          day: 7,
          date: "4/5",
          location: "Fujinomiya/Shizuoka",
          mainActivityTitle: "Mt. Fuji Satoyama E-Bike Tour & Nature Exploration",
          narrative: ["Breakfast at the Mt. Fuji Glamping Site."],
          activities: [
            {
              title: "Morning Tour to Shiraito Falls",
              description: ["If you're up for an early start, take a refreshing morning walk along a quiet village path to the majestic Shiraito Falls, ideally after enjoying the beautiful sunrise."]
            },
            {
              title: "Satoyama E-Bike Eco Tour",
              description: ["Explore the countryside on an e-bike eco tour through the scenic satoyama landscape, with views of Mt. Fuji in the background. A knowledgeable local guide will accompany you, sharing insights into the area's culture, traditions, and way of life along the way."]
            },
            {
              title: "Lunch - Handmade Soba Noodles",
              description: ["Enjoy handmade soba noodles prepared by a local group of mothers, using traditional techniques and seasonal ingredients. Later, transfer to Shizuoka for the night."]
            }
          ],
          images: [
            { src: "bikes fuji.png", alt: "E-bikes with Mt. Fuji in the background" },
            { src: "waterfall.png", alt: "Shiraito Falls" },
            { src: "soba noodles.webp", alt: "Handmade soba noodles" },
            { src: "bking.png", alt: "E-biking experience" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "NIHON IRO",
            meals: "B Included", 
            link: "https://nihoniro.jp/en/",
            stars: "★★★★"
          }
        },
        {
          day: 8,
          date: "4/6",
          location: "Shizuoka",
          mainActivityTitle: "Artisan Craft Experience & Tea Farm Visit",
          narrative: ["Breakfast at the Hotel.", "This area is known for its fresh seafood. Feel free to choose where to eat for dinner—whether it's a Japanese seafood restaurant, a lively izakaya, a cozy dining café, or even an Italian or French-style restaurant. Enjoy dinner wherever suits your taste and mood."],
          activities: [
            {
              title: "Hands-on Craft Activity at Takumishuku",
              description: ["Begin with a hands-on workshop at Takumishuku, where you can try your hand at traditional crafts such as chopstick making. You'll be able to take home your personally crafted item as a memorable keepsake."]
            },
            {
              title: "Shizuoka Tea Farm Visit & Tasting Experience",
              description: ["At a local tea farm, enjoy a tasting experience of Shizuoka's distinct teas while taking in views of the tea fields. Learn directly from the tea farmer about the region's tea culture and how to brew delicious tea. Optional: Relax at a nearby café and enjoy tea-infused sweets and desserts."]
            },
            {
              title: "Lunch at a Local Restaurant",
              description: ["Enjoy lunch at a local restaurant with your group."]
            },
            {
              title: "Free Time for Evening",
              description: ["The evening is free for you to explore Shizuoka's dining scene at your leisure."]
            }
          ],
          images: [
            { src: "making chopsticks.png", alt: "Hands crafting chopsticks" },
            { src: "japanese-tea-farm_1.jpg", alt: "Tea fields in Shizuoka" },
            { src: "chopsticks.png", alt: "Traditional Japanese chopsticks" }
          ],
          mealsIncluded: "Breakfast, Lunch",
          transportation: "Private chartered mid-size bus",
          notes: "Other craft options such as bamboo craft or pottery making may also be available upon request.",
          accommodation: {
            name: "NIHON IRO",
            meals: "B Included",
            link: "https://nihoniro.jp/en/",
            stars: "★★★★"
          }
        },
        {
          day: 9,
          date: "4/7",
          location: "Kyoto (from Shizuoka) & Nara",
          mainActivityTitle: "Travel to Kyoto & Afternoon Excursion to Nara",
          narrative: [
            "Breakfast at the Hotel.",
            "Travel to Kyoto via Shinkansen (approximately 2 hours). Upon arrival at Kyoto Station, you will be met by your guide and vehicle, enjoy lunch, and then proceed for an afternoon excursion to Nara.",
            "In the afternoon, take a historical journey to Nara, once the ancient capital of Japan. Begin with a visit to Tōdai-ji Temple, a UNESCO World Heritage site and home to the Great Buddha (Daibutsu), one of the largest bronze Buddha statues in the world, housed within an immense wooden hall.",
            "After exploring Tōdai-ji, you'll head by vehicle to the summit of Mt. Wakakusa, located just behind Nara Park. This mountaintop experience is more of a local secret, offering panoramic views of the city and temple rooftops stretching below. Here you'll encounter some of the relaxed deer that roam the peaceful summit, revered as messengers of the gods in Japanese tradition.",
            "Afterward, continue on to Kyoto, with an expected arrival between 5:00 and 6:00 PM."
          ],
          activities: [
            {
              title: "Afternoon Excursion to Nara",
              description: [
                "Explore Nara Park, interacting with its famous resident deer.",
                "Visit Todai-ji Temple, marveling at the Great Buddha statue and the temple's grand wooden architecture."
              ]
            }
          ],
          images: [
            { src: "nara deer.jpeg", alt: "Friendly deer in Nara Park" },
            { src: "toykyo temple.png", alt: "Traditional Japanese temple" },
            { src: "shinkansen.webp", alt: "Shinkansen bullet train" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Shinkansen and Private chartered mid-size bus",
          accommodation: {
            name: "Mitsui Garden Hotel Kyoto Sanjo Premier",
            meals: "B Included",
            link: "https://www.gardenhotels.co.jp/kyoto-sanjo-premier/eng/",
            stars: "★★★★★"
          }
        },
        {
          day: 10,
          date: "4/8",
          location: "Kyoto",
          mainActivityTitle: "Kyoto: Fushimi Inari, Kinkakuji & Taiko Drumming",
          narrative: [
            "Breakfast at the Hotel.",
            "Spend the day discovering two of Kyoto's most iconic landmarks with your guide. Begin at Fushimi Inari Taisha, famous for its thousands of vermilion torii gates winding through the forested hillside. Then visit the Golden Pavilion (Kinkakuji), a stunning Zen temple covered in gold leaf, reflecting beautifully on its surrounding pond. This day offers a deep glimpse into the spiritual and architectural beauty of Japan's former capital.",
            "Enjoy a hands-on introduction to traditional Japanese culture through Wadaiko (Japanese drums). Led by expert instructors from Taiko Center, you'll learn the basics of playing Taiko and experience the energy and spirit of this iconic art form.",
            "Free time for evening: Kyoto is home to an incredible variety of dining options. Take this evening to enjoy dinner at a spot that catches your eye—your guide will be happy to offer recommendations. Ninja Cafe, where you can enjoy a ninja training experience in costume along with food, is one fun and unique option."
          ],
          activities: [
            {
              title: "Fushimi Inari & Kinkakuji Visit",
              description: ["Explore the thousands of red torii gates at Fushimi Inari Shrine and marvel at the gold-leafed Kinkakuji (Golden Pavilion)."]
            },
            {
              title: "Taiko Drumming Experience",
              description: ["Immerse yourself in the powerful rhythms of Japan with an exhilarating Taiko drumming workshop at the Taiko Center."]
            }
          ],
          images: [
            { src: "fushimi inari shrine.jpeg", alt: "Thousands of red torii gates at Fushimi Inari Shrine" },
            { src: "Kinkakuji Visit.webp", alt: "Golden Pavilion (Kinkakuji) in Kyoto" },
            { src: "taiko drums.png", alt: "Traditional Taiko drumming performance" }
          ],
          mealsIncluded: "Breakfast, Lunch",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "Mitsui Garden Hotel Kyoto Sanjo Premier",
            meals: "B Included",
            link: "https://www.gardenhotels.co.jp/kyoto-sanjo-premier/eng/",
            stars: "★★★★★"
          }
        },
        {
          day: 11,
          date: "4/9",
          location: "Kyoto",
          mainActivityTitle: "Samurai Experience, Tea Ceremony & Farewell Dinner",
          narrative: ["Breakfast at the Hotel."],
          activities: [
            {
              title: "Samurai Experience",
              description: ["In the morning, take a deep dive into the mindset and training of the samurai. Dressed in traditional Bushido attire, you'll learn how the philosophy of Zen is intertwined with swordsmanship—and even try your hand at wielding a sword, now that you understand its cultural significance."]
            },
            {
              title: "Traditional Tea Ceremony",
              description: ["In the afternoon, immerse yourself in the refined art of the Japanese tea ceremony. Guided by a kimono-clad host, you'll learn about the cultural significance of matcha and observe the graceful rituals involved in its preparation. This serene experience offers a moment of calm and reflection, rooted in centuries of tradition."]
            },
            {
              title: "Farewell Dinner at a Local Restaurant",
              description: ["Enjoy a final dinner together with the group you've shared this journey with—a chance to reflect on the experiences, connections, and memories made along the way."]
            }
          ],
          images: [
            { src: "samurai training.png", alt: "Samurai training session" },
            { src: "tea ceremony.png", alt: "Traditional Japanese tea ceremony" }
          ],
          mealsIncluded: "Breakfast, Lunch, Dinner",
          transportation: "Private chartered mid-size bus",
          accommodation: {
            name: "Mitsui Garden Hotel Kyoto Sanjo Premier",
            meals: "B Included",
            link: "https://www.gardenhotels.co.jp/kyoto-sanjo-premier/eng/",
            stars: "★★★★★"
          }
        },
        {
          day: 12,
          date: "4/10",
          location: "Osaka (Departure from KIX/ITM)",
          mainActivityTitle: "Departure",
          narrative: ["After breakfast, you will be transferred to Kansai International Airport (KIX) or Osaka International Airport (ITM) by private chartered mid-size bus for your departure. Sayonara and safe travels!"],
          activities: [],
          images: [
            { src: "departure flight.png", alt: "Airport scene with a departing airplane" }
          ],
          mealsIncluded: "Breakfast",
          transportation: "Private chartered mid-size bus (Airport Transfer)",
          accommodation: {
            name: "N/A - Departure Day",
            meals: "-",
            link: "",
          }
        }
      ]
    };

    // Icon Definitions
    const createIconSVG = (pathData, viewBox = "0 0 20 20", fillRule = "evenodd", clipRule = "evenodd", extraClass = "") => {
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="${viewBox}" fill="currentColor" class="icon ${extraClass}"><path fill-rule="${fillRule}" d="${pathData}" clip-rule="${clipRule}" /></svg>`;
    };
    
    const icons = {
        toriiGate: createIconSVG("M18 4h-2v2h-2V4H6v2H4V4H2v14h2v-4h12v4h2V4zM6 10v2h8v-2H6z", "0 0 20 20", "nonzero"), 
        calendarDays: createIconSVG("M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"),
        risingSun: createIconSVG("M10 3a7 7 0 100 14 7 7 0 000-14zm0 1a6 6 0 110 12 6 6 0 010-12zm0 1.5a.5.5 0 00-.5.5v2a.5.5 0 001 0v-2a.5.5 0 00-.5-.5zM3.222 7.5H1.5a.5.5 0 000 1h1.722a.5.5 0 000-1zm13.056 0H14.5a.5.5 0 000 1h1.778a.5.5 0 000-1zM10 13.5l-3-3h6l-3 3z", "0 0 20 20", "nonzero"), 
        pagoda: createIconSVG("M10 2L3 7v1h14V7L10 2zm-6 7v8h3v-4h6v4h3V9H4zm4 1h4v2H8v-2z", "0 0 20 20", "nonzero"), 
        link: createIconSVG("M12.232 4.232a2.5 2.5 0 013.536 3.536l-1.225 1.224a.75.75 0 001.061 1.06l1.224-1.224a4 4 0 00-5.656-5.656l-3 3a4 4 0 00.225 5.865.75.75 0 00.977-1.138 2.5 2.5 0 01-.142-3.667l3-3z M11.603 7.963a.75.75 0 00-.977 1.138 2.5 2.5 0 01.142 3.667l-3 3a2.5 2.5 0 01-3.536-3.536l1.225-1.224a.75.75 0 00-1.061-1.06l-1.224 1.224a4 4 0 105.656 5.656l3-3a4 4 0 00-.225-5.865z", "0 0 20 20", "nonzero"),
        omamori: createIconSVG("M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zm0 1h6a1 1 0 011 1v1H6V4a1 1 0 011-1zm-2 3h10v1H5V6zm2 1.5a.5.5 0 01.5-.5h3a.5.5 0 010 1h-3a.5.5 0 01-.5-.5zM5 10h10v1H5v-1zm5 4a1.5 1.5 0 110-3 1.5 1.5 0 010 3z", "0 0 20 20", "nonzero"), 
        sakura: createIconSVG("M10 5.382l.927-1.854a.5.5 0 01.854-.146l1.396 1.396a.5.5 0 00.707 0l1.396-1.396a.5.5 0 01.854.146L16.118 5.382a.5.5 0 00.464.464l1.854.927a.5.5 0 01.146.854l-1.396 1.396a.5.5 0 000 .707l1.396 1.396a.5.5 0 01-.146.854L16.582 11.8a.5.5 0 00-.464.464l-.927 1.854a.5.5 0 01-.854.146l-1.396-1.396a.5.5 0 00-.707 0l-1.396 1.396a.5.5 0 01-.854-.146L8.122 12.263a.5.5 0 00-.464-.464L5.804 10.87a.5.5 0 01-.146-.854l1.396-1.396a.5.5 0 000-.707L5.658 6.517a.5.5 0 01.146-.854l1.854-.927a.5.5 0 00.464-.464L9.045 3.5a.5.5 0 01.854-.146L10 5.382zM10 8a2 2 0 100 4 2 2 0 000-4z", "0 0 20 20", "nonzero"), 
        riceBowl: createIconSVG("M4 9.5c0 1.728 2.686 3.5 6 3.5s6-1.772 6-3.5V9H4v.5zM3 9h14v1H3V9zm1 2.5a.5.5 0 000 1h12a.5.5 0 000-1H4zM6.5 5c0 .552.895 1 2 1h3c1.105 0 2-.448 2-1H6.5zM6 4h8V3H6v1z", "0 0 20 20", "nonzero"), 
        shinkansen: createIconSVG("M5 4a2 2 0 00-2 2v6a2 2 0 002 2h1V9H5V4zm12 0h-1v11h1a2 2 0 002-2V6a2 2 0 00-2-2zm-8 0h4v2H9V4zm0 3h4v2H9V7zm0 3h4v2H9v-2zm-2.5 3.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z", "0 0 20 20", "nonzero"), 
        chevronDown: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6 text-primary-text transform transition-transform duration-200"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>`
    };

    const accordionContainer = document.getElementById('itinerary-accordion-container');
    const toggleAllButton = document.getElementById('toggle-all-accordions');
    let allAccordionsOpen = false;
    
    itineraryData.dailyPlans.forEach((day) => {
      const dayElement = document.createElement('div');
      dayElement.id = `day-${day.day}`; 
      dayElement.className = 'card overflow-hidden';
      const contentInitialClass = 'accordion-content';
      const ariaExpandedInitial = 'false';

      // Generate image HTML if images exist for the day
      let imagesHTML = '';
      if (day.images && day.images.length > 0) {
        imagesHTML = `
          <div class="itinerary-image-container">
            ${day.images.map(img => `
              <div class="itinerary-image-item">
                <img src="${img.src}" alt="${img.alt}" loading="lazy" onerror="this.onerror=null;this.parentElement.innerHTML='<div class=\\'w-full h-48 bg-gray-200 flex items-center justify-center text-gray-500 rounded-lg\\'>${img.alt} (Image not found)</div>';">
              </div>
            `).join('')}
          </div>
        `;
      }

      dayElement.innerHTML = `
        <button
          aria-expanded="${ariaExpandedInitial}" 
          aria-controls="day-content-${day.day}"
          class="accordion-toggle w-full flex justify-between items-center p-4 sm:p-6 text-left bg-custom-accordion-header hover:bg-opacity-75 transition-colors duration-200"
        >
          <div class="flex items-center">
             ${icons.calendarDays.replace('fill="currentColor"', 'fill="var(--color-heading-accent)"').replace('class="icon "', 'class="icon w-7 h-7"')}
             <span class="ml-3 text-xl md:text-2xl font-semibold text-primary-text">${day.day === 1 || day.day === 12 ? '' : `Day ${day.day}: `}${day.mainActivityTitle}</span>
          </div>
          <span class="chevron-icon">${icons.chevronDown.replace('w-6 h-6', 'w-7 h-7')}</span>
        </button>
        <div
          id="day-content-${day.day}"
          class="${contentInitialClass}" 
          role="region"
          aria-labelledby="day-header-${day.day}"
        >
          <div class="p-4 sm:p-6 border-t border-custom">
            <div class="mb-6 flex flex-wrap gap-x-8 gap-y-3 text-lg text-primary-text">
              <span class="flex items-center">${icons.toriiGate.replace('fill="currentColor"', 'fill="var(--color-secondary-accent)"')}<strong>Location:</strong>&nbsp;${day.location}</span>
              <span class="flex items-center">${icons.risingSun.replace('fill="currentColor"', 'fill="var(--color-icon-neutral)"')}<strong>Date:</strong>&nbsp;${day.date}</span>
            </div>

            ${imagesHTML} 

            ${day.narrative && day.narrative.length > 0 ? `
              <div class="mb-6 space-y-3 text-primary-text prose-lg max-w-none">
                ${day.narrative.map(p => `<p>${p}</p>`).join('')}
              </div>
            ` : ''}
            
            ${day.activities && day.activities.length > 0 ? day.activities.map(activity => `
              <div class="mb-6 p-4 border border-custom rounded-lg bg-custom-light shadow-sm">
                <h4 class="text-xl md:text-2xl font-semibold text-heading-accent mb-3 flex items-center">
                  ${icons.sakura.replace('fill="currentColor"', 'fill="var(--color-heading-accent)"')}
                  <span class="ml-2">${activity.title}</span>
                </h4>
                ${activity.description.map(desc => `<p class="text-primary-text mb-2 text-lg leading-relaxed">${desc}</p>`).join('')}
              </div>
            `).join('') : ''}
            
            <div class="mt-8 space-y-2 text-lg leading-relaxed">
              <p class="flex items-center text-primary-text">${icons.riceBowl.replace('fill="currentColor"', 'fill="var(--color-secondary-accent)"')}<strong>Meals Included:</strong>&nbsp;${day.mealsIncluded}</p>
              <p class="flex items-center text-primary-text">${icons.shinkansen.replace('fill="currentColor"', 'fill="var(--color-icon-neutral)"')}<strong>Transportation:</strong>&nbsp;${day.transportation}</p>
              ${day.notes ? `<p class="flex items-start text-primary-text">${icons.omamori.replace('fill="currentColor"', 'fill="var(--color-icon-neutral)"')}<strong>Note:</strong>&nbsp;${day.notes}</p>` : ''}
            </div>
            
            ${day.accommodation && day.accommodation.name !== "N/A - Departure Day" ? `
            <div class="mt-8 p-4 border border-custom rounded-lg bg-sky-50 shadow-sm bg-custom-light">
              <h4 class="text-xl md:text-2xl font-semibold text-secondary-accent mb-3 flex items-center">
                ${icons.pagoda.replace('fill="currentColor"', 'fill="var(--color-secondary-accent)"')}
                <span class="ml-2">Accommodation: ${day.accommodation.name} <span class="text-gold ml-2">${day.accommodation.stars}</span></span>
              </h4>
              <p class="text-primary-text text-lg leading-relaxed"><strong class="font-medium">Meals at Hotel:</strong> ${day.accommodation.meals}</p>
              ${day.accommodation.link ? `
                <a href="${day.accommodation.link}" target="_blank" rel="noopener noreferrer" class="mt-2 inline-flex items-center text-secondary-accent hover:text-opacity-80 text-lg leading-relaxed">
                  ${icons.link.replace('fill="currentColor"', 'fill="var(--color-secondary-accent)"')} Visit Hotel Website
                </a>
              ` : ''}
            </div>` : ''}
          </div>
        </div>
      `;
      accordionContainer.appendChild(dayElement);

      const toggleButton = dayElement.querySelector('.accordion-toggle');
      const content = dayElement.querySelector('.accordion-content');
      const chevron = toggleButton.querySelector('.chevron-icon svg');

      if (chevron) {
        chevron.style.transform = content.classList.contains('open') ? 'rotate(180deg)' : '';
      }
      
      toggleButton.addEventListener('click', () => {
        const isOpen = content.classList.toggle('open');
        toggleButton.setAttribute('aria-expanded', isOpen);
        if (chevron) {
            chevron.style.transform = isOpen ? 'rotate(180deg)' : '';
        }
        
        const allToggles = document.querySelectorAll('.accordion-toggle');
        let allCurrentlyOpen = true;
        let allCurrentlyClosed = true;
        allToggles.forEach(btn => {
            if (btn.getAttribute('aria-expanded') === 'false') {
                allCurrentlyOpen = false;
            } else {
                allCurrentlyClosed = false;
            }
        });

        if (allCurrentlyOpen) {
            allAccordionsOpen = true;
            toggleAllButton.textContent = 'Close All Days';
        } else if (allCurrentlyClosed) {
            allAccordionsOpen = false;
            toggleAllButton.textContent = 'Open All Days';
        }
        // If mixed state, button text doesn't need to change from its current state unless all are opened/closed.
      });
    });
    
    if (toggleAllButton) {
        toggleAllButton.addEventListener('click', () => {
            allAccordionsOpen = !allAccordionsOpen; 
            const allAccordionToggles = document.querySelectorAll('.accordion-toggle');
            
            allAccordionToggles.forEach(button => {
                const contentId = button.getAttribute('aria-controls');
                const contentElement = document.getElementById(contentId);
                const chevron = button.querySelector('.chevron-icon svg');

                if (allAccordionsOpen) { 
                    contentElement.classList.add('open');
                    button.setAttribute('aria-expanded', 'true');
                    if (chevron) chevron.style.transform = 'rotate(180deg)';
                } else { 
                    contentElement.classList.remove('open');
                    button.setAttribute('aria-expanded', 'false');
                    if (chevron) chevron.style.transform = '';
                }
            });
            toggleAllButton.textContent = allAccordionsOpen ? 'Close All Days' : 'Open All Days';
        });
    }

    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (scrollToTopBtn) {
        window.onscroll = function() {
          if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
            scrollToTopBtn.classList.remove('hidden');
            scrollToTopBtn.classList.add('block'); 
          } else {
            scrollToTopBtn.classList.remove('block');
            scrollToTopBtn.classList.add('hidden');
          }
        };

        scrollToTopBtn.onclick = function() {
          window.scrollTo({top: 0, behavior: 'smooth'});
        };
    }

    const dayNavigation = document.getElementById('day-navigation');
    const dayNavUl = dayNavigation?.querySelector('ul');
    const heroSection = document.getElementById('hero');
    const mainHeader = document.getElementById('main-header');

    if (dayNavUl && itineraryData && heroSection && mainHeader) {
      itineraryData.dailyPlans.forEach(plan => {
        const li = document.createElement('li');
        li.setAttribute('role', 'presentation');
        const a = document.createElement('a');
        a.href = `#day-${plan.day}`;
        if (plan.day === 1 || plan.day === 12) {
            a.textContent = plan.mainActivityTitle.length > 20 ? `Day ${plan.day}` : plan.mainActivityTitle;
        } else {
            a.textContent = `Day ${plan.day}`;
        }
        a.setAttribute('role', 'menuitem');
        a.setAttribute('title', `Day ${plan.day}: ${plan.mainActivityTitle}`); 
        
        a.addEventListener('click', function(event) {
          event.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            const headerOffset = mainHeader.offsetHeight + 20; 
            const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
            const offsetPosition = elementPosition - headerOffset;
            
            window.scrollTo({
                top: offsetPosition,
                behavior: "smooth"
            });
          }
        });

        li.appendChild(a);
        dayNavUl.appendChild(li);
      });

      window.addEventListener('scroll', () => {
        const heroHeight = heroSection.offsetHeight;
        const headerHeight = mainHeader.offsetHeight;
        if (window.scrollY > (heroHeight - headerHeight - 20)) {
          dayNavigation.classList.remove('hidden');
        } else {
          dayNavigation.classList.add('hidden');
        }
      });
    }
  </script>
</body>
</html>
