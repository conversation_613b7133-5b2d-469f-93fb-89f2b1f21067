const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Create a compressed directory if it doesn't exist
const compressedDir = 'compressed';
if (!fs.existsSync(compressedDir)) {
    fs.mkdirSync(compressedDir);
}

// Get all image files
const imageFiles = fs.readdirSync('.').filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.webp'].includes(ext);
});

// Process each image
async function compressImages() {
    for (const file of imageFiles) {
        const ext = path.extname(file).toLowerCase();
        const outputPath = path.join(compressedDir, file);
        
        try {
            // Read the image
            const image = sharp(file);
            const metadata = await image.metadata();
            
            // Calculate new dimensions while maintaining aspect ratio
            let width = metadata.width;
            let height = metadata.height;
            
            // If image is larger than 1200px in any dimension, resize it
            if (width > 1200 || height > 1200) {
                if (width > height) {
                    height = Math.round((height * 1200) / width);
                    width = 1200;
                } else {
                    width = Math.round((width * 1200) / height);
                    height = 1200;
                }
            }
            
            // Process the image
            await image
                .resize(width, height, {
                    fit: 'inside',
                    withoutEnlargement: true
                })
                .jpeg({ quality: 80, progressive: true }) // Convert to JPEG for better compression
                .toFile(outputPath);
            
            console.log(`Compressed ${file} -> ${outputPath}`);
        } catch (error) {
            console.error(`Error processing ${file}:`, error);
        }
    }
}

compressImages().then(() => {
    console.log('Image compression completed!');
}); 